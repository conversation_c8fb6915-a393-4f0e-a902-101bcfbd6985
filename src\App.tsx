import React, { useState } from 'react';
import { useInventory } from './hooks/useInventory';
import { MovementForm } from './components/MovementForm';
import { WarehouseCard } from './components/WarehouseCard';
import { InventorySummary } from './components/InventorySummary';
import { Button } from './components/ui/button';
import { Label } from './components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from './components/ui/select';
import { ValuationMethod } from './types/inventory';
import { getValuationMethodName } from './utils/formatters';
import { exportData, importData, clearAllData } from './utils/storage';
import {
  FileDown,
  FileUp,
  Trash2,
  Package,
  BarChart3,
  Settings
} from 'lucide-react';

function App() {
  const {
    state,
    isLoading,
    addMovement,
    removeMovement,
    changeValuationMethod,
    clearAllData: clearInventoryData,
    getInventorySummary,
  } = useInventory();

  const [activeTab, setActiveTab] = useState<'summary' | 'warehouse' | 'settings'>('summary');

  const handleExportData = () => {
    try {
      const data = exportData();
      const blob = new Blob([data], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `inventario-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      alert('Error al exportar los datos');
    }
  };

  const handleImportData = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = (e) => {
          try {
            const data = e.target?.result as string;
            importData(data);
            window.location.reload(); // Recargar para aplicar los cambios
          } catch (error) {
            alert('Error al importar los datos. Verifique el formato del archivo.');
          }
        };
        reader.readAsText(file);
      }
    };
    input.click();
  };

  const handleClearData = () => {
    if (confirm('¿Está seguro de que desea eliminar todos los datos? Esta acción no se puede deshacer.')) {
      clearAllData();
      clearInventoryData();
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p>Cargando...</p>
        </div>
      </div>
    );
  }

  const summary = getInventorySummary();

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b border-border bg-card">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold">Tarjeta de Almacén Digital</h1>
              <p className="text-sm text-muted-foreground">
                Sistema de gestión de inventarios con métodos PEPS, UEPS y Promedio Ponderado
              </p>
            </div>
            <div className="flex items-center gap-2">
              <MovementForm onAddMovement={addMovement} />
            </div>
          </div>
        </div>
      </header>

      {/* Navigation */}
      <nav className="border-b border-border bg-card">
        <div className="container mx-auto px-4">
          <div className="flex space-x-8">
            <button
              onClick={() => setActiveTab('summary')}
              className={`py-4 px-2 border-b-2 font-medium text-sm flex items-center gap-2 ${
                activeTab === 'summary'
                  ? 'border-primary text-primary'
                  : 'border-transparent text-muted-foreground hover:text-foreground'
              }`}
            >
              <BarChart3 className="h-4 w-4" />
              Resumen
            </button>
            <button
              onClick={() => setActiveTab('warehouse')}
              className={`py-4 px-2 border-b-2 font-medium text-sm flex items-center gap-2 ${
                activeTab === 'warehouse'
                  ? 'border-primary text-primary'
                  : 'border-transparent text-muted-foreground hover:text-foreground'
              }`}
            >
              <Package className="h-4 w-4" />
              Tarjeta de Almacén
            </button>
            <button
              onClick={() => setActiveTab('settings')}
              className={`py-4 px-2 border-b-2 font-medium text-sm flex items-center gap-2 ${
                activeTab === 'settings'
                  ? 'border-primary text-primary'
                  : 'border-transparent text-muted-foreground hover:text-foreground'
              }`}
            >
              <Settings className="h-4 w-4" />
              Configuración
            </button>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-6">
        {activeTab === 'summary' && (
          <InventorySummary
            summary={summary}
            valuationMethod={state.valuationMethod}
          />
        )}

        {activeTab === 'warehouse' && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-semibold">Tarjeta de Almacén</h2>
              <div className="text-sm text-muted-foreground">
                Método: {getValuationMethodName(state.valuationMethod)}
              </div>
            </div>
            <WarehouseCard
              entries={state.warehouseCard}
              onRemoveMovement={removeMovement}
            />
          </div>
        )}

        {activeTab === 'settings' && (
          <div className="space-y-6 max-w-2xl">
            <h2 className="text-xl font-semibold">Configuración</h2>

            {/* Método de valoración */}
            <div className="bg-card border border-border rounded-lg p-6">
              <h3 className="text-lg font-medium mb-4">Método de Valoración</h3>
              <div className="space-y-2">
                <Label htmlFor="valuation-method">Seleccione el método de valoración</Label>
                <Select
                  value={state.valuationMethod}
                  onValueChange={(value: ValuationMethod) => changeValuationMethod(value)}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="PEPS">PEPS (Primeras Entradas, Primeras Salidas)</SelectItem>
                    <SelectItem value="UEPS">UEPS (Últimas Entradas, Primeras Salidas)</SelectItem>
                    <SelectItem value="PROMEDIO_PONDERADO">Promedio Ponderado</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-sm text-muted-foreground">
                  Cambiar el método recalculará automáticamente toda la tarjeta de almacén.
                </p>
              </div>
            </div>

            {/* Gestión de datos */}
            <div className="bg-card border border-border rounded-lg p-6">
              <h3 className="text-lg font-medium mb-4">Gestión de Datos</h3>
              <div className="space-y-4">
                <div className="flex flex-col sm:flex-row gap-2">
                  <Button onClick={handleExportData} variant="outline" className="flex items-center gap-2">
                    <FileDown className="h-4 w-4" />
                    Exportar Datos
                  </Button>
                  <Button onClick={handleImportData} variant="outline" className="flex items-center gap-2">
                    <FileUp className="h-4 w-4" />
                    Importar Datos
                  </Button>
                </div>
                <div className="border-t border-border pt-4">
                  <Button
                    onClick={handleClearData}
                    variant="destructive"
                    className="flex items-center gap-2"
                  >
                    <Trash2 className="h-4 w-4" />
                    Eliminar Todos los Datos
                  </Button>
                  <p className="text-sm text-muted-foreground mt-2">
                    Esta acción eliminará permanentemente todos los movimientos y configuraciones.
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}
      </main>
    </div>
  );
}

export default App;
