import React from 'react';
import { WarehouseCardEntry } from '@/types/inventory';
import { formatCurrency, formatDate, getOperationTypeName, getOperationTypeColor } from '@/utils/formatters';
import { Button } from '@/components/ui/button';
import { Trash2 } from 'lucide-react';

interface WarehouseCardProps {
  entries: WarehouseCardEntry[];
  onRemoveMovement?: (movementId: string) => void;
}

export const WarehouseCard: React.FC<WarehouseCardProps> = ({ 
  entries, 
  onRemoveMovement 
}) => {
  if (entries.length === 0) {
    return (
      <div className="text-center py-8 text-muted-foreground">
        <p>No hay movimientos registrados</p>
        <p className="text-sm">Agregue el primer movimiento para comenzar</p>
      </div>
    );
  }

  return (
    <div className="overflow-x-auto">
      <table className="w-full border-collapse border border-border">
        <thead>
          <tr className="bg-muted">
            <th className="border border-border px-2 py-2 text-left text-sm font-medium">Fecha</th>
            <th className="border border-border px-2 py-2 text-left text-sm font-medium">Operación</th>
            <th className="border border-border px-2 py-2 text-left text-sm font-medium">Descripción</th>
            <th className="border border-border px-2 py-2 text-center text-sm font-medium" colSpan={3}>
              Entradas
            </th>
            <th className="border border-border px-2 py-2 text-center text-sm font-medium" colSpan={3}>
              Salidas
            </th>
            <th className="border border-border px-2 py-2 text-center text-sm font-medium" colSpan={3}>
              Existencias
            </th>
            {onRemoveMovement && (
              <th className="border border-border px-2 py-2 text-center text-sm font-medium">Acciones</th>
            )}
          </tr>
          <tr className="bg-muted/50">
            <th className="border border-border px-2 py-1"></th>
            <th className="border border-border px-2 py-1"></th>
            <th className="border border-border px-2 py-1"></th>
            <th className="border border-border px-2 py-1 text-xs">Cant.</th>
            <th className="border border-border px-2 py-1 text-xs">P. Unit.</th>
            <th className="border border-border px-2 py-1 text-xs">Total</th>
            <th className="border border-border px-2 py-1 text-xs">Cant.</th>
            <th className="border border-border px-2 py-1 text-xs">P. Unit.</th>
            <th className="border border-border px-2 py-1 text-xs">Total</th>
            <th className="border border-border px-2 py-1 text-xs">Cant.</th>
            <th className="border border-border px-2 py-1 text-xs">P. Unit.</th>
            <th className="border border-border px-2 py-1 text-xs">Total</th>
            {onRemoveMovement && (
              <th className="border border-border px-2 py-1"></th>
            )}
          </tr>
        </thead>
        <tbody>
          {entries.map((entry) => (
            <tr key={entry.id} className="hover:bg-muted/30">
              <td className="border border-border px-2 py-2 text-sm">
                {formatDate(entry.date)}
              </td>
              <td className="border border-border px-2 py-2">
                <span className={`inline-block px-2 py-1 rounded text-xs font-medium ${getOperationTypeColor(entry.type)}`}>
                  {getOperationTypeName(entry.type)}
                </span>
              </td>
              <td className="border border-border px-2 py-2 text-sm">
                {entry.description || '-'}
              </td>
              
              {/* Entradas */}
              <td className="border border-border px-2 py-2 text-sm text-right">
                {entry.entryQuantity ? entry.entryQuantity.toLocaleString('es-CO', { minimumFractionDigits: 2 }) : '-'}
              </td>
              <td className="border border-border px-2 py-2 text-sm text-right">
                {entry.entryUnitPrice ? formatCurrency(entry.entryUnitPrice) : '-'}
              </td>
              <td className="border border-border px-2 py-2 text-sm text-right">
                {entry.entryTotal ? formatCurrency(entry.entryTotal) : '-'}
              </td>
              
              {/* Salidas */}
              <td className="border border-border px-2 py-2 text-sm text-right">
                {entry.exitQuantity ? entry.exitQuantity.toLocaleString('es-CO', { minimumFractionDigits: 2 }) : '-'}
              </td>
              <td className="border border-border px-2 py-2 text-sm text-right">
                {entry.exitUnitPrice ? formatCurrency(entry.exitUnitPrice) : '-'}
              </td>
              <td className="border border-border px-2 py-2 text-sm text-right">
                {entry.exitTotal ? formatCurrency(entry.exitTotal) : '-'}
              </td>
              
              {/* Existencias */}
              <td className="border border-border px-2 py-2 text-sm text-right font-medium">
                {entry.balanceQuantity.toLocaleString('es-CO', { minimumFractionDigits: 2 })}
              </td>
              <td className="border border-border px-2 py-2 text-sm text-right font-medium">
                {formatCurrency(entry.balanceUnitPrice)}
              </td>
              <td className="border border-border px-2 py-2 text-sm text-right font-medium">
                {formatCurrency(entry.balanceTotal)}
              </td>
              
              {/* Acciones */}
              {onRemoveMovement && (
                <td className="border border-border px-2 py-2 text-center">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onRemoveMovement(entry.id)}
                    className="h-8 w-8 p-0 text-destructive hover:text-destructive"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </td>
              )}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};
