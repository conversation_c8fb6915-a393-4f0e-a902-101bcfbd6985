import { useState, useEffect, useCallback } from 'react';
import {
  InventoryState,
  InventoryMovement,
  ValuationMethod,
  InventorySummary,
} from '../types/inventory';
import { calculateInventory, generateId } from '../utils/inventoryCalculations';
import { saveInventoryState, loadInventoryState } from '../utils/storage';

const initialState: InventoryState = {
  movements: [],
  warehouseCard: [],
  lots: [],
  currentStock: 0,
  currentValue: 0,
  averageUnitCost: 0,
  valuationMethod: 'PROMEDIO_PONDERADO',
};

export const useInventory = () => {
  const [state, setState] = useState<InventoryState>(initialState);
  const [isLoading, setIsLoading] = useState(true);

  // Cargar datos al inicializar
  useEffect(() => {
    const loadData = () => {
      try {
        const savedState = loadInventoryState();
        if (savedState) {
          setState(savedState);
        }
      } catch (error) {
        console.error('Error al cargar datos:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, []);

  // Guardar datos cuando el estado cambie
  useEffect(() => {
    if (!isLoading) {
      try {
        saveInventoryState(state);
      } catch (error) {
        console.error('Error al guardar datos:', error);
      }
    }
  }, [state, isLoading]);

  // Recalcular inventario
  const recalculateInventory = useCallback((movements: InventoryMovement[], method: ValuationMethod) => {
    const { warehouseCard, lots } = calculateInventory(movements, method);
    
    const currentStock = warehouseCard.length > 0 
      ? warehouseCard[warehouseCard.length - 1].balanceQuantity 
      : 0;
    
    const currentValue = warehouseCard.length > 0 
      ? warehouseCard[warehouseCard.length - 1].balanceTotal 
      : 0;
    
    const averageUnitCost = currentStock > 0 ? currentValue / currentStock : 0;

    return {
      movements,
      warehouseCard,
      lots,
      currentStock,
      currentValue,
      averageUnitCost,
      valuationMethod: method,
    };
  }, []);

  // Agregar movimiento
  const addMovement = useCallback((movement: Omit<InventoryMovement, 'id'>) => {
    const newMovement: InventoryMovement = {
      ...movement,
      id: generateId(),
      total: movement.quantity * movement.unitPrice,
    };

    const newMovements = [...state.movements, newMovement];
    const newState = recalculateInventory(newMovements, state.valuationMethod);
    setState(newState);
  }, [state.movements, state.valuationMethod, recalculateInventory]);

  // Eliminar movimiento
  const removeMovement = useCallback((movementId: string) => {
    const newMovements = state.movements.filter(m => m.id !== movementId);
    const newState = recalculateInventory(newMovements, state.valuationMethod);
    setState(newState);
  }, [state.movements, state.valuationMethod, recalculateInventory]);

  // Cambiar método de valoración
  const changeValuationMethod = useCallback((method: ValuationMethod) => {
    const newState = recalculateInventory(state.movements, method);
    setState(newState);
  }, [state.movements, recalculateInventory]);

  // Limpiar todos los datos
  const clearAllData = useCallback(() => {
    setState(initialState);
  }, []);

  // Obtener resumen del inventario
  const getInventorySummary = useCallback((): InventorySummary => {
    const totalMovements = state.movements.length;
    const totalPurchases = state.movements.filter(m => m.type === 'COMPRA').length;
    const totalSales = state.movements.filter(m => m.type === 'VENTA').length;
    const totalReturns = state.movements.filter(m => 
      m.type === 'DEVOLUCION_VENTA' || m.type === 'DEVOLUCION_COMPRA'
    ).length;

    return {
      totalMovements,
      totalPurchases,
      totalSales,
      totalReturns,
      currentStock: state.currentStock,
      currentValue: state.currentValue,
      averageUnitCost: state.averageUnitCost,
    };
  }, [state]);

  return {
    state,
    isLoading,
    addMovement,
    removeMovement,
    changeValuationMethod,
    clearAllData,
    getInventorySummary,
  };
};
