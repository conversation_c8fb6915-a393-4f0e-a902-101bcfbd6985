import React from 'react';
import { InventorySummary as InventorySummaryType, ValuationMethod } from '@/types/inventory';
import { formatCurrency, getValuationMethodName } from '@/utils/formatters';
import { Package, TrendingUp, TrendingDown, RotateCcw, Calculator } from 'lucide-react';

interface InventorySummaryProps {
  summary: InventorySummaryType;
  valuationMethod: ValuationMethod;
}

export const InventorySummary: React.FC<InventorySummaryProps> = ({ 
  summary, 
  valuationMethod 
}) => {
  const summaryCards = [
    {
      title: 'Stock Actual',
      value: summary.currentStock.toLocaleString('es-CO', { minimumFractionDigits: 2 }),
      icon: Package,
      color: 'text-blue-600 bg-blue-50',
    },
    {
      title: 'Valor del Inventario',
      value: formatCurrency(summary.currentValue),
      icon: Calculator,
      color: 'text-green-600 bg-green-50',
    },
    {
      title: 'Costo Promedio Unitario',
      value: formatCurrency(summary.averageUnitCost),
      icon: TrendingUp,
      color: 'text-purple-600 bg-purple-50',
    },
    {
      title: 'Total Compras',
      value: summary.totalPurchases.toString(),
      icon: TrendingUp,
      color: 'text-green-600 bg-green-50',
    },
    {
      title: 'Total Ventas',
      value: summary.totalSales.toString(),
      icon: TrendingDown,
      color: 'text-red-600 bg-red-50',
    },
    {
      title: 'Total Devoluciones',
      value: summary.totalReturns.toString(),
      icon: RotateCcw,
      color: 'text-orange-600 bg-orange-50',
    },
  ];

  return (
    <div className="space-y-6">
      {/* Método de valoración */}
      <div className="bg-card border border-border rounded-lg p-4">
        <h3 className="text-lg font-semibold mb-2">Método de Valoración</h3>
        <p className="text-sm text-muted-foreground">
          {getValuationMethodName(valuationMethod)}
        </p>
      </div>

      {/* Tarjetas de resumen */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {summaryCards.map((card, index) => {
          const Icon = card.icon;
          return (
            <div key={index} className="bg-card border border-border rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">
                    {card.title}
                  </p>
                  <p className="text-2xl font-bold">
                    {card.value}
                  </p>
                </div>
                <div className={`p-3 rounded-full ${card.color}`}>
                  <Icon className="h-6 w-6" />
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Estadísticas adicionales */}
      <div className="bg-card border border-border rounded-lg p-4">
        <h3 className="text-lg font-semibold mb-4">Estadísticas Generales</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
          <div>
            <p className="text-2xl font-bold text-primary">{summary.totalMovements}</p>
            <p className="text-sm text-muted-foreground">Total Movimientos</p>
          </div>
          <div>
            <p className="text-2xl font-bold text-green-600">{summary.totalPurchases}</p>
            <p className="text-sm text-muted-foreground">Compras</p>
          </div>
          <div>
            <p className="text-2xl font-bold text-red-600">{summary.totalSales}</p>
            <p className="text-sm text-muted-foreground">Ventas</p>
          </div>
          <div>
            <p className="text-2xl font-bold text-orange-600">{summary.totalReturns}</p>
            <p className="text-sm text-muted-foreground">Devoluciones</p>
          </div>
        </div>
      </div>
    </div>
  );
};
