import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { OperationType } from '@/types/inventory';
import { getOperationTypeName, getCurrentISODate } from '@/utils/formatters';
import { Plus } from 'lucide-react';

interface MovementFormProps {
  onAddMovement: (movement: {
    date: string;
    type: OperationType;
    quantity: number;
    unitPrice: number;
    description?: string;
  }) => void;
}

export const MovementForm: React.FC<MovementFormProps> = ({ onAddMovement }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [formData, setFormData] = useState({
    date: getCurrentISODate().split('T')[0], // Solo la fecha, sin la hora
    type: '' as OperationType,
    quantity: '',
    unitPrice: '',
    description: '',
  });

  const operationTypes: OperationType[] = ['COMPRA', 'VENTA', 'DEVOLUCION_VENTA', 'DEVOLUCION_COMPRA'];

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.type || !formData.quantity || !formData.unitPrice) {
      alert('Por favor complete todos los campos obligatorios');
      return;
    }

    const quantity = parseFloat(formData.quantity);
    const unitPrice = parseFloat(formData.unitPrice);

    if (quantity <= 0 || unitPrice <= 0) {
      alert('La cantidad y el precio unitario deben ser mayores a cero');
      return;
    }

    onAddMovement({
      date: new Date(formData.date).toISOString(),
      type: formData.type,
      quantity,
      unitPrice,
      description: formData.description || undefined,
    });

    // Resetear formulario
    setFormData({
      date: getCurrentISODate().split('T')[0],
      type: '' as OperationType,
      quantity: '',
      unitPrice: '',
      description: '',
    });
    
    setIsOpen(false);
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          Nuevo Movimiento
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Agregar Movimiento de Inventario</DialogTitle>
          <DialogDescription>
            Complete los datos del movimiento de inventario.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="date">Fecha *</Label>
            <Input
              id="date"
              type="date"
              value={formData.date}
              onChange={(e) => handleInputChange('date', e.target.value)}
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="type">Tipo de Operación *</Label>
            <Select value={formData.type} onValueChange={(value) => handleInputChange('type', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Seleccione el tipo de operación" />
              </SelectTrigger>
              <SelectContent>
                {operationTypes.map((type) => (
                  <SelectItem key={type} value={type}>
                    {getOperationTypeName(type)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="quantity">Cantidad *</Label>
            <Input
              id="quantity"
              type="number"
              step="0.01"
              min="0.01"
              value={formData.quantity}
              onChange={(e) => handleInputChange('quantity', e.target.value)}
              placeholder="Ingrese la cantidad"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="unitPrice">Precio Unitario *</Label>
            <Input
              id="unitPrice"
              type="number"
              step="0.01"
              min="0.01"
              value={formData.unitPrice}
              onChange={(e) => handleInputChange('unitPrice', e.target.value)}
              placeholder="Ingrese el precio unitario"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Descripción</Label>
            <Input
              id="description"
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              placeholder="Descripción opcional del movimiento"
            />
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => setIsOpen(false)}>
              Cancelar
            </Button>
            <Button type="submit">
              Agregar Movimiento
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};
